<template>
  <div class="home-container">
    <!-- 头部导航 -->
    <div class="header">
      <div class="header-content">
        <h1 class="logo">音频转文字</h1>
        <div class="user-info">
          <span>欢迎，{{ maskPhone(authStore.user?.phone || '') }}</span>
          <a-button type="text" @click="handleLogout">
            <LogoutOutlined />
            退出登录
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="[24, 24]">
        <!-- 上传区域 -->
        <a-col :xs="24" :lg="12">
          <a-card title="音频上传" class="upload-card">
            <div class="upload-area">
              <a-upload-dragger
                :before-upload="handleBeforeUpload"
                :show-upload-list="false"
                :disabled="audioStore.isProcessing"
                accept=".mp3,.wav,.m4a,.aac,.flac"
                class="upload-dragger"
              >
                <div class="upload-content">
                  <p class="ant-upload-drag-icon">
                    <CloudUploadOutlined />
                  </p>
                  <p class="ant-upload-text">
                    {{ audioStore.isProcessing ? '正在处理中...' : '点击或拖拽文件到此区域上传' }}
                  </p>
                  <p class="ant-upload-hint">
                    支持格式：MP3、WAV、M4A、AAC、FLAC<br />
                    文件大小限制：100MB
                  </p>
                </div>
              </a-upload-dragger>
            </div>

            <!-- 当前任务信息 -->
            <div v-if="audioStore.currentTask" class="current-task">
              <a-divider>当前任务</a-divider>
              <div class="task-info">
                <div class="file-info">
                  <AudioOutlined />
                  <span class="file-name">{{ audioStore.currentTask.file.name }}</span>
                  <span class="file-size"
                    >({{ formatFileSize(audioStore.currentTask.file.size) }})</span
                  >
                </div>

                <div class="task-status">
                  <a-progress
                    :percent="audioStore.currentTask.progress"
                    :status="getProgressStatus(audioStore.currentTask.status)"
                    :show-info="true"
                  />
                  <div class="status-text">
                    {{ getStatusText(audioStore.currentTask.status) }}
                  </div>
                </div>

                <div v-if="audioStore.currentTask.error" class="error-message">
                  <a-alert :message="audioStore.currentTask.error" type="error" show-icon />
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 结果展示区域 -->
        <a-col :xs="24" :lg="12">
          <a-card title="转换结果" class="result-card">
            <div
              v-if="!audioStore.currentTask?.result && !audioStore.currentTask?.error"
              class="empty-result"
            >
              <a-empty description="暂无转换结果" />
            </div>

            <div v-else-if="audioStore.currentTask?.result" class="result-content">
              <a-textarea
                v-model:value="audioStore.currentTask.result"
                :rows="12"
                readonly
                class="result-textarea"
              />

              <div class="result-actions">
                <a-space>
                  <a-button type="primary" @click="copyResult" :loading="copyLoading">
                    <CopyOutlined />
                    复制文本
                  </a-button>
                  <a-button @click="downloadResult">
                    <DownloadOutlined />
                    下载文件
                  </a-button>
                </a-space>
              </div>

              <div
                v-if="audioStore.currentTask.startTime && audioStore.currentTask.endTime"
                class="task-stats"
              >
                <a-divider />
                <div class="stats-info">
                  <span
                    >转换耗时：{{
                      formatDuration(
                        audioStore.currentTask.endTime - audioStore.currentTask.startTime,
                      )
                    }}</span
                  >
                  <span>文件大小：{{ formatFileSize(audioStore.currentTask.file.size) }}</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 历史记录 -->
      <div v-if="audioStore.history.length > 0" class="history-section">
        <a-card title="历史记录">
          <template #extra>
            <a-button type="text" danger @click="clearHistory">
              <DeleteOutlined />
              清空记录
            </a-button>
          </template>

          <a-list
            :data-source="audioStore.history"
            :pagination="{ pageSize: 5, showSizeChanger: false }"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a-button type="text" @click="copyHistoryResult(item.result!)">
                    <CopyOutlined />
                  </a-button>
                  <a-button
                    type="text"
                    @click="downloadHistoryResult(item.result!, item.file.name)"
                  >
                    <DownloadOutlined />
                  </a-button>
                </template>

                <a-list-item-meta>
                  <template #title>
                    <span>{{ item.file.name }}</span>
                    <a-tag v-if="item.status === 'completed'" color="success">已完成</a-tag>
                    <a-tag v-else-if="item.status === 'error'" color="error">失败</a-tag>
                  </template>
                  <template #description>
                    <div>
                      <span>大小：{{ formatFileSize(item.file.size) }}</span>
                      <span v-if="item.startTime" style="margin-left: 16px">
                        时间：{{ new Date(item.startTime).toLocaleString() }}
                      </span>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  LogoutOutlined,
  CloudUploadOutlined,
  AudioOutlined,
  CopyOutlined,
  DownloadOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useAudioStore } from '@/stores/audio'
import {
  validateFileType,
  validateFileSize,
  formatFileSize,
  formatDuration,
  maskPhone,
} from '@/utils/validation'
import { SUPPORTED_AUDIO_FORMATS, MAX_FILE_SIZE, CONVERSION_STATUS } from '@/utils/constants'

const router = useRouter()
const authStore = useAuthStore()
const audioStore = useAudioStore()

const copyLoading = ref(false)

// 处理文件上传前的验证
const handleBeforeUpload = (file: File) => {
  // 验证文件类型
  if (!validateFileType(file, SUPPORTED_AUDIO_FORMATS)) {
    message.error('不支持的文件格式，请上传 MP3、WAV、M4A、AAC 或 FLAC 格式的音频文件')
    return false
  }

  // 验证文件大小
  if (!validateFileSize(file, MAX_FILE_SIZE)) {
    message.error('文件大小不能超过 100MB')
    return false
  }

  // 开始上传
  audioStore.uploadAudio(file).catch((error) => {
    console.error('上传失败:', error)
  })

  return false // 阻止默认上传行为
}

// 获取进度条状态
const getProgressStatus = (status: string) => {
  switch (status) {
    case CONVERSION_STATUS.COMPLETED:
      return 'success'
    case CONVERSION_STATUS.ERROR:
      return 'exception'
    default:
      return 'active'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case CONVERSION_STATUS.UPLOADING:
      return '正在上传...'
    case CONVERSION_STATUS.PROCESSING:
      return '正在转换...'
    case CONVERSION_STATUS.COMPLETED:
      return '转换完成'
    case CONVERSION_STATUS.ERROR:
      return '转换失败'
    default:
      return '等待中...'
  }
}

// 复制结果
const copyResult = async () => {
  if (!audioStore.currentTask?.result) return

  copyLoading.value = true
  try {
    const success = await audioStore.copyToClipboard(audioStore.currentTask.result)
    if (success) {
      message.success('已复制到剪贴板')
    } else {
      message.error('复制失败')
    }
  } finally {
    copyLoading.value = false
  }
}

// 下载结果
const downloadResult = () => {
  if (!audioStore.currentTask?.result) return

  const filename = audioStore.currentTask.file.name.replace(/\.[^/.]+$/, '') + '_转换结果.txt'
  audioStore.downloadText(audioStore.currentTask.result, filename)
  message.success('文件下载已开始')
}

// 复制历史记录结果
const copyHistoryResult = async (text: string) => {
  const success = await audioStore.copyToClipboard(text)
  if (success) {
    message.success('已复制到剪贴板')
  } else {
    message.error('复制失败')
  }
}

// 下载历史记录结果
const downloadHistoryResult = (text: string, originalFilename: string) => {
  const filename = originalFilename.replace(/\.[^/.]+$/, '') + '_转换结果.txt'
  audioStore.downloadText(text, filename)
  message.success('文件下载已开始')
}

// 清空历史记录
const clearHistory = () => {
  audioStore.clearHistory()
  message.success('历史记录已清空')
}

// 退出登录
const handleLogout = () => {
  authStore.logout()
  message.success('已退出登录')
  router.push('/login')
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.logo {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #6b7280;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.upload-card,
.result-card {
  height: fit-content;
}

.upload-area {
  margin-bottom: 24px;
}

.upload-dragger {
  background: #fafafa;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  transition: all 0.3s;
}

.upload-dragger:hover {
  border-color: #1890ff;
}

.upload-content {
  padding: 40px 20px;
}

.upload-content .ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
}

.upload-content .ant-upload-text {
  font-size: 16px;
  color: #1f2937;
  margin-bottom: 8px;
}

.upload-content .ant-upload-hint {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.current-task {
  margin-top: 24px;
}

.task-info {
  space-y: 16px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.file-name {
  font-weight: 500;
  color: #1f2937;
}

.file-size {
  color: #6b7280;
  font-size: 14px;
}

.task-status {
  margin-bottom: 16px;
}

.status-text {
  margin-top: 8px;
  color: #6b7280;
  font-size: 14px;
}

.error-message {
  margin-top: 16px;
}

.empty-result {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.result-content {
  space-y: 16px;
}

.result-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.result-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.task-stats {
  margin-top: 16px;
}

.stats-info {
  display: flex;
  gap: 24px;
  color: #6b7280;
  font-size: 14px;
}

.history-section {
  margin-top: 32px;
}

@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }

  .header-content {
    flex-direction: column;
    height: auto;
    padding: 16px 0;
    gap: 16px;
  }

  .user-info {
    flex-direction: column;
    gap: 8px;
  }

  .main-content {
    padding: 16px;
  }

  .upload-content {
    padding: 24px 16px;
  }

  .upload-content .ant-upload-drag-icon {
    font-size: 36px;
  }

  .stats-info {
    flex-direction: column;
    gap: 8px;
  }

  .result-actions {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .file-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .result-actions .ant-space {
    width: 100%;
  }

  .result-actions .ant-space .ant-btn {
    flex: 1;
  }
}
</style>
