import { PHONE_REGEX, SMS_CODE_LENGTH } from './constants'

/**
 * 验证手机号格式
 */
export const validatePhone = (phone: string): boolean => {
  return PHONE_REGEX.test(phone)
}

/**
 * 验证短信验证码格式
 */
export const validateSmsCode = (code: string): boolean => {
  return code.length === SMS_CODE_LENGTH && /^\d+$/.test(code)
}

/**
 * 验证激活码格式（假设为8位字母数字组合）
 */
export const validateActivationCode = (code: string): boolean => {
  return code.length >= 6 && /^[A-Za-z0-9]+$/.test(code)
}

/**
 * 验证文件类型
 */
export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
  return allowedTypes.includes(fileExtension)
}

/**
 * 验证文件大小
 */
export const validateFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize
}

/**
 * 格式化文件大小显示
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化时间显示（毫秒转秒）
 */
export const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60

  if (minutes > 0) {
    return `${minutes}分${remainingSeconds}秒`
  }
  return `${remainingSeconds}秒`
}

/**
 * 手机号脱敏显示
 */
export const maskPhone = (phone: string): string => {
  if (!phone || phone.length !== 11) return phone
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}
