import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import request, { uploadFile } from '@/utils/request'
import { CONVERSION_STATUS } from '@/utils/constants'

export interface AudioFile {
  id: string
  name: string
  size: number
  type: string
  url?: string
}

export interface ConversionTask {
  id: string
  file: AudioFile
  status: string
  progress: number
  result?: string
  error?: string
  startTime?: number
  endTime?: number
}

export const useAudioStore = defineStore('audio', () => {
  const currentTask = ref<ConversionTask | null>(null)
  const history = ref<ConversionTask[]>([])
  const uploading = ref(false)
  const uploadProgress = ref(0)

  const isProcessing = computed(
    () =>
      currentTask.value?.status === CONVERSION_STATUS.UPLOADING ||
      currentTask.value?.status === CONVERSION_STATUS.PROCESSING,
  )

  // 上传音频文件
  const uploadAudio = async (file: File) => {
    uploading.value = true
    uploadProgress.value = 0

    const task: ConversionTask = {
      id: Date.now().toString(),
      file: {
        id: Date.now().toString(),
        name: file.name,
        size: file.size,
        type: file.type,
      },
      status: CONVERSION_STATUS.UPLOADING,
      progress: 0,
      startTime: Date.now(),
    }

    currentTask.value = task

    try {
      // 上传文件
      const response = await uploadFile('/audio/upload', file, (progress) => {
        uploadProgress.value = progress
        if (currentTask.value) {
          currentTask.value.progress = progress
        }
      })

      if (currentTask.value) {
        currentTask.value.status = CONVERSION_STATUS.PROCESSING
        currentTask.value.file.url = (response as any).data.fileUrl
      }

      // 开始转换
      await startConversion((response as any).data.taskId)
    } catch (error) {
      if (currentTask.value) {
        currentTask.value.status = CONVERSION_STATUS.ERROR
        currentTask.value.error = error instanceof Error ? error.message : '上传失败'
      }
      throw error
    } finally {
      uploading.value = false
    }
  }

  // 开始转换
  const startConversion = async (taskId: string) => {
    if (!currentTask.value) return

    try {
      // 轮询检查转换状态
      const checkStatus = async (): Promise<void> => {
        const response = await request.get(`/audio/convert/${taskId}`)

        if (currentTask.value) {
          currentTask.value.status = response.data.status
          currentTask.value.progress = response.data.progress || 0
        }

        if (response.data.status === CONVERSION_STATUS.COMPLETED) {
          // 获取转换结果
          const resultResponse = await request.get(`/audio/result/${taskId}`)
          if (currentTask.value) {
            currentTask.value.result = resultResponse.data.text
            currentTask.value.endTime = Date.now()
            currentTask.value.progress = 100
          }

          // 添加到历史记录
          if (currentTask.value) {
            history.value.unshift({ ...currentTask.value })
          }
        } else if (response.data.status === CONVERSION_STATUS.ERROR) {
          if (currentTask.value) {
            currentTask.value.error = response.data.error || '转换失败'
            currentTask.value.endTime = Date.now()
          }
        } else {
          // 继续轮询
          setTimeout(checkStatus, 2000)
        }
      }

      await checkStatus()
    } catch (error) {
      if (currentTask.value) {
        currentTask.value.status = CONVERSION_STATUS.ERROR
        currentTask.value.error = error instanceof Error ? error.message : '转换失败'
        currentTask.value.endTime = Date.now()
      }
      throw error
    }
  }

  // 重置当前任务
  const resetCurrentTask = () => {
    currentTask.value = null
    uploadProgress.value = 0
  }

  // 复制文本到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      return true
    } catch {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      const success = document.execCommand('copy')
      document.body.removeChild(textArea)
      return success
    }
  }

  // 下载文本文件
  const downloadText = (text: string, filename: string = 'transcription.txt') => {
    const blob = new Blob([text], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // 清除历史记录
  const clearHistory = () => {
    history.value = []
  }

  return {
    currentTask,
    history,
    uploading,
    uploadProgress,
    isProcessing,
    uploadAudio,
    resetCurrentTask,
    copyToClipboard,
    downloadText,
    clearHistory,
  }
})
